import React, { useState, useRef, useEffect } from 'react'
import { Button, Upload, message, Card, Space, Typography, Divider, Radio, Modal } from 'antd'
import { UploadOutlined, CheckCircleOutlined, LoadingOutlined, SendOutlined, FullscreenOutlined, CopyOutlined } from '@ant-design/icons'
import * as XLSX from 'xlsx'

import { getUserId } from '@src/common/utils/userConfigApi'
import { PROJECTID } from '@src/config/aiChatConfig'

const { Text, Paragraph } = Typography



// L1分类选项
const L1_CATEGORIES = [
  '对话',
  '对话GUI',
  '大盘',
  '个股详情',
  '交易',
  '账户',
  '资讯',
  '监测',
  '一句话盯盘',
  '热点选股',
  '自选',
  '资金',
  '触发',
  '消息',
  '搜索',
  '开机屏',
  '探索',
  '侧边栏',
  '陪伴',
  '涨停选股',
  '持股'
]

const ExcelProcessor: React.FC = () => {
  const [l1Category, setL1Category] = useState('')
  const [loading, setLoading] = useState(false)
  const [questions, setQuestions] = useState<string[]>([])
  const [processing, setProcessing] = useState(false)
  const [streamContent, setStreamContent] = useState('')
  const [isStreamComplete, setIsStreamComplete] = useState(false)
  const [userId, setUserId] = useState('')
  const [conversationId, setConversationId] = useState('')
  const [hasError, setHasError] = useState(false)
  const [showFullscreenModal, setShowFullscreenModal] = useState(false)
  const streamContentRef = useRef<HTMLDivElement>(null)

  // 初始化用户ID和会话ID
  useEffect(() => {
    const initializeData = async () => {
      try {
        const uid = await getUserId()
        setUserId(uid)
        // 生成一个新的会话ID
        setConversationId(`excel-processor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`)
      } catch (error) {
        console.error('初始化用户数据失败:', error)
        setUserId('anonymous_user')
        setConversationId(`excel-processor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`)
      }
    }
    initializeData()
  }, [])

  // 自动滚动到底部
  useEffect(() => {
    if (streamContentRef.current) {
      streamContentRef.current.scrollTop = streamContentRef.current.scrollHeight
    }
  }, [streamContent])

  const handleUpload = async (file: File) => {
    try {
      setLoading(true)

      // 检查文件类型
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        message.error('请上传Excel文件 (.xlsx 或 .xls)')
        return false
      }

      if (!l1Category) {
        message.error('请输入L1分类名称')
        return false
      }

      // 读取Excel文件
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })

      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]

      // 转换为JSON格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet)

      // 查找L1分类列名（支持多种格式）
      const l1ColumnNames = ['L1（反馈功能）', '反馈功能']
      let l1ColumnName = ''

      // 检查哪个列名存在
      for (const columnName of l1ColumnNames) {
        if (jsonData.length > 0 && jsonData[0].hasOwnProperty(columnName)) {
          l1ColumnName = columnName
          break
        }
      }

      if (!l1ColumnName) {
        message.error(`未找到L1分类列，请确保Excel中包含以下列名之一：${l1ColumnNames.join('、')}`)
        return false
      }

      // 筛选出L1分类等于用户输入的L1分类的数据
      const filteredData = jsonData.filter((row: any) =>
        row[l1ColumnName] === l1Category
      )

      if (filteredData.length === 0) {
        message.warning(`未找到"${l1ColumnName}"为"${l1Category}"的数据`)
        return false
      }

      // 查找问题列名（支持多种格式）
      const questionColumnNames = ['用户问题', '反馈描述']
      let questionColumnName = ''

      // 检查哪个列名存在
      for (const columnName of questionColumnNames) {
        if (filteredData.length > 0 && filteredData[0].hasOwnProperty(columnName)) {
          questionColumnName = columnName
          break
        }
      }

      if (!questionColumnName) {
        message.error(`未找到问题列，请确保Excel中包含以下列名之一：${questionColumnNames.join('、')}`)
        return false
      }

      // 提取问题列的内容
      const extractedQuestions = filteredData
        .map((row: any) => row[questionColumnName])
        .filter((question: any) => question) // 过滤掉空值

      if (extractedQuestions.length === 0) {
        message.warning(`未找到有效的"${questionColumnName}"数据`)
        return false
      }

      // 设置问题数据
      setQuestions(extractedQuestions)
      // 重置处理状态
      setStreamContent('')
      setIsStreamComplete(false)
      setHasError(false)
      message.success(`成功解析${extractedQuestions.length}条问题，请点击确认按钮提交处理`)
    } catch (error) {
      console.error('处理Excel文件时出错:', error)
      message.error('处理文件时发生错误')
    } finally {
      setLoading(false)
    }

    return false // 阻止默认上传行为
  }

  // 处理流式API请求
  const processQuestionsWithStream = async () => {
    try {
      setProcessing(true)
      setStreamContent('')
      setIsStreamComplete(false)
      setHasError(false)

      const requestData = {
        skillId: 55,
        appId: "web-assistant",
        userId: userId,
        agentId: "customize",
        conversationId: conversationId,
        question: JSON.stringify({
          l1Category: l1Category,
          questions: questions
        }),
        questionExtends: {},
        enableInternetSearch: true,
        messageInterval: 50,
        perMessageNum: 1,
        actions: [
          {
            name: "insertDom",
            description: "处理翻译API返回值，更新页面翻译图标"
          },
          {
            name: "updateModalData",
            description: "更新和拼接处理流式消息数据，用于划词工具栏显示"
          }
        ],
        hideConversation: false,
        projectId: PROJECTID,
        searchOptions: {
          provider: [],
          snippet: false,
          browserToolClientId: `user_${userId}_7x59iht5y`,
          authInfos: {}
        }
      }

      const response = await fetch('https://webassist.saassit.htsc.com.cn/chat/workflow/chrome-v2', {
        method: 'POST',
        headers: {
          'Referer': '',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'text/event-stream',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Response body is not readable')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          setIsStreamComplete(true)
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              const jsonStr = line.slice(5).trim() // 移除 'data:' 前缀
              if (jsonStr === '[DONE]') {
                setIsStreamComplete(true)
                break
              }

              const data = JSON.parse(jsonStr)
              console.log('Parsed SSE data:', data)

              // 检查数据结构：data.resultData.answerInfos[0].text
              if (data.resultData && data.resultData.answerInfos && data.resultData.answerInfos.length > 0) {
                const answerInfo = data.resultData.answerInfos[0]
                if (answerInfo.text) {
                  try {
                    // answerInfo.text 本身是一个JSON字符串，需要再次解析
                    const textData = JSON.parse(answerInfo.text)
                    console.log('Parsed text data:', textData)

                    // 提取实际的文本内容
                    if (textData.list && textData.list.length > 0) {
                      for (const item of textData.list) {
                        if (item.type === 'text' && item.content && item.content.text) {
                          setStreamContent(prev => prev + item.content.text)
                        }
                      }
                    }
                  } catch (textParseError) {
                    console.warn('Failed to parse text data:', textParseError)
                    // 如果解析失败，直接使用原始text
                    setStreamContent(prev => prev + answerInfo.text)
                  }
                }
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line, e)
            }
          }
        }
      }

      message.success('问题处理完成')
    } catch (error) {
      console.error('处理问题时出错:', error)
      message.error('处理问题时发生错误，请重试')
      setHasError(true)
      setIsStreamComplete(true)
    } finally {
      setProcessing(false)
    }
  }

  const beforeUpload = (file: File) => {
    handleUpload(file)
    return false
  }

  // 复制内容到剪贴板
  const handleCopyContent = async () => {
    if (!streamContent) {
      message.warning('暂无内容可复制')
      return
    }

    try {
      await navigator.clipboard.writeText(streamContent)
      message.success('内容已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      // 降级方案：使用传统的复制方法
      try {
        const textArea = document.createElement('textarea')
        textArea.value = streamContent
        textArea.style.position = 'fixed'
        textArea.style.opacity = '0'
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        message.success('内容已复制到剪贴板')
      } catch (fallbackError) {
        console.error('降级复制也失败:', fallbackError)
        message.error('复制失败，请手动选择内容复制')
      }
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      <Card title="Excel问题处理器">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong style={{ marginBottom: '12px', display: 'block' }}>选择L1分类：</Text>
            <Radio.Group
              value={l1Category}
              onChange={(e) => setL1Category(e.target.value)}
              style={{ width: '100%' }}
            >
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                gap: '8px 16px',
                marginTop: '8px'
              }}>
                {L1_CATEGORIES.map(category => (
                  <Radio key={category} value={category}>
                    {category}
                  </Radio>
                ))}
              </div>
            </Radio.Group>
          </div>

          <Upload
            beforeUpload={beforeUpload}
            maxCount={1}
            accept=".xlsx,.xls"
          >
            <Button
              icon={<UploadOutlined />}
              loading={loading}
              disabled={!l1Category}
            >
              上传Excel文件
            </Button>
          </Upload>

          <div style={{ marginTop: '20px' }}>
            <h3>使用说明：</h3>
            <ul>
              <li>选择L1分类名称</li>
              <li>上传Excel文件（支持.xlsx和.xls格式）</li>
              <li>系统将筛选出L1分类列等于您选择的分类名称的所有行</li>
              <li>支持的L1分类列名：<strong>"L1（反馈功能）"</strong> 或 <strong>"反馈功能"</strong></li>
              <li>支持的问题列名：<strong>"用户问题"</strong> 或 <strong>"反馈描述"</strong></li>
              <li>提取符合条件的行中的问题列内容，确认后提交AI处理</li>
              <li>处理结果将以流式方式实时显示</li>
            </ul>
          </div>
        </Space>
      </Card>

      {/* 解析结果确认区域 */}
      {questions.length > 0 && (
        <Card
          title="解析结果确认"
          style={{ marginTop: '20px' }}
          extra={
            <Button
              type="primary"
              icon={<SendOutlined />}
              loading={processing}
              onClick={processQuestionsWithStream}
              disabled={processing}
            >
              {hasError ? '重新处理' : '确认处理'}
            </Button>
          }
        >
          <div>
            <Text strong>L1分类：</Text>
            <Text>{l1Category}</Text>
          </div>
          <Divider />
          <div>
            <Text strong>共找到 {questions.length} 条问题：</Text>
            <div style={{
              maxHeight: '300px',
              overflowY: 'auto',
              marginTop: '10px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '12px',
              backgroundColor: '#fafafa'
            }}>
              {questions.map((question, index) => (
                <Paragraph key={index} style={{ marginBottom: '8px' }}>
                  <Text type="secondary">{index + 1}. </Text>
                  {question}
                </Paragraph>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* 流式输出结果 */}
      {(processing || streamContent || isStreamComplete) && (
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {processing ? <LoadingOutlined /> : <CheckCircleOutlined style={{ color: '#52c41a' }} />}
              <span>处理结果</span>
              {processing && <Text type="secondary">（正在处理中...）</Text>}
            </div>
          }
          extra={
            <Space>
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={handleCopyContent}
                title="复制内容"
                disabled={!streamContent}
              />
              <Button
                type="text"
                icon={<FullscreenOutlined />}
                onClick={() => setShowFullscreenModal(true)}
                title="全屏查看"
              />
            </Space>
          }
          style={{ marginTop: '20px' }}
        >
          <div
            ref={streamContentRef}
            style={{
              minHeight: '200px',
              maxHeight: '500px',
              overflowY: 'auto',
              padding: '12px',
              backgroundColor: '#fafafa',
              border: '1px solid #d9d9d9',
              borderRadius: '6px'
            }}
          >
            {streamContent ? (
              <div style={{
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace',
                fontSize: '14px',
                lineHeight: '1.6'
              }}>
                {streamContent}
                {processing && (
                  <span style={{ color: '#1890ff' }}>▋</span>
                )}
              </div>
            ) : (
              <div style={{
                fontSize: '14px',
                lineHeight: '1.6',
                color: '#666',
                fontStyle: 'italic'
              }}>
                {processing ? '正在处理中，请稍候...' : '等待处理结果...'}
              </div>
            )}
          </div>
          {isStreamComplete && (
            <div style={{ marginTop: '12px', textAlign: 'center' }}>
              <Text type="success">
                <CheckCircleOutlined style={{ marginRight: '4px' }} />
                处理完成
              </Text>
            </div>
          )}
        </Card>
      )}

      {/* 全屏查看Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {processing ? <LoadingOutlined /> : <CheckCircleOutlined style={{ color: '#52c41a' }} />}
              <span>处理结果 - 全屏查看</span>
              {processing && <Text type="secondary">（正在处理中...）</Text>}
            </div>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={handleCopyContent}
              title="复制内容"
              disabled={!streamContent}
              size="small"
            />
          </div>
        }
        open={showFullscreenModal}
        onCancel={() => setShowFullscreenModal(false)}
        footer={null}
        width="95vw"
        style={{ top: 20 }}
        styles={{
          body: {
            height: 'calc(95vh - 120px)',
            padding: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <div
          style={{
            height: '100%',
            overflowY: 'auto',
            padding: '16px',
            backgroundColor: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        >
          {streamContent ? (
            <div style={{
              whiteSpace: 'pre-wrap',
              fontFamily: 'monospace',
              fontSize: '14px',
              lineHeight: '1.6'
            }}>
              {streamContent}
              {processing && (
                <span style={{ color: '#1890ff' }}>▋</span>
              )}
            </div>
          ) : (
            <div style={{
              fontSize: '14px',
              lineHeight: '1.6',
              color: '#666',
              fontStyle: 'italic'
            }}>
              {processing ? '正在处理中，请稍候...' : '暂无处理结果'}
            </div>
          )}
        </div>
        {isStreamComplete && (
          <div style={{ marginTop: '12px', textAlign: 'center' }}>
            <Text type="success">
              <CheckCircleOutlined style={{ marginRight: '4px' }} />
              处理完成
            </Text>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ExcelProcessor