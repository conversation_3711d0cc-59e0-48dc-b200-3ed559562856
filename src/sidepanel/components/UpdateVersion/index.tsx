import React, { useState } from 'react'
import { update } from '@src/common/images'
import { getBaseUrl } from '@src/common/utils/baseUrl'
import ReactMarkdown from 'react-markdown'
import { Modal, Button } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import './style.less'

interface UpdateVersionProps {
  latestVersion: string | null
  updateType: number | null // 0: 强制更新, 1: 非强制更新
  description?: string | null // 版本更新描述
}

const UpdateVersion: React.FC<UpdateVersionProps> = ({ latestVersion, updateType, description }) => {
  const [showSkipConfirm, setShowSkipConfirm] = useState(false)

  const handleDownloadClick = async () => {
    try {
      const baseUrl = getBaseUrl()
      const downloadUrl = `${baseUrl}${baseUrl.includes('eiplite') ? '' : '/webassist'}/webAssistant/index.html#/download`;
      console.log('downloadUrl', downloadUrl)
      await chrome.tabs.create({
        url: downloadUrl,
      })
    } catch (error) {
      console.error('Failed to open download page:', error)
      console.error('Download page navigation failed')
    }
  }

  const handleSkipClick = () => {
    if (latestVersion) {
      try {
        const skippedVersionInfo = {
          version: latestVersion,
          skipped: true,
          timestamp: Date.now()
        }
        localStorage.setItem('skippedVersionInfo', JSON.stringify(skippedVersionInfo))
        window.location.reload()
      } catch (error) {
        console.error('跳过版本时出错:', error)
      }
    }
  }

  const confirmSkip = () => {
    setShowSkipConfirm(true)
  }

  const cancelSkip = () => {
    setShowSkipConfirm(false)
  }

  const confirmSkipAction = () => {
    setShowSkipConfirm(false)
    handleSkipClick()
  }

  // Custom components for ReactMarkdown to ensure proper list rendering
  const markdownComponents = {
    ul: ({ children }: any) => (
      <ul style={{ paddingLeft: '20px', marginBottom: '0.5em' }}>
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol style={{ paddingLeft: '20px', marginBottom: '0.5em', listStyleType: 'decimal' }}>
        {children}
      </ol>
    ),
    li: ({ children }: any) => (
      <li style={{ marginBottom: '0.3em' }}>
        {children}
      </li>
    ),
    p: ({ children }: any) => (
      <p style={{ margin: '0.5em 0' }}>
        {children}
      </p>
    )
  }

  return (
    <div className="updateVersionContainer">
      <img className="updateImg" src={update} alt="update" />
      <div className="title">插件版本更新</div>

      {/* 显示版本更新描述 */}
      {description && (
        <div className="description">
          <ReactMarkdown components={markdownComponents}>
            {description}
          </ReactMarkdown>
        </div>
      )}

      <div className="buttonContainer">
        {updateType === 1 && ( // updateType === 1 表示非强制更新
          <div className="skipBtn" onClick={confirmSkip}>跳过</div>
        )}
        <div className="downloadBtn" onClick={handleDownloadClick}>点击下载</div>

      </div>

      {/* 跳过确认弹窗 */}
      <Modal
        open={showSkipConfirm}
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ExclamationCircleFilled style={{ color: 'red', marginRight: 8, fontSize: '18px' }} />
            <span>是否确认跳过该版本？</span>
          </div>
        }
        onCancel={cancelSkip}
        centered
        footer={[
          <Button key="back" onClick={cancelSkip}>
            取消
          </Button>,
          <Button key="submit" type="primary" danger onClick={confirmSkipAction}>
            确认跳过
          </Button>,
        ]}
        closable={true}
      >
        <p style={{ color: '#6d6d6d' }}>跳过该版本后，在下次版本更新前将不再提示。</p>
      </Modal>
    </div>
  )
}

export default UpdateVersion