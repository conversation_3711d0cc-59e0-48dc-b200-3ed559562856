.updateVersionContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;

  .updateImg {
    width: 160px;
    height: 160px;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .description {
    max-width: 80%;
    margin-bottom: 20px;
    text-align: left;
    color: #666;
    line-height: 1.6;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      color: #333;
    }

    p {
      margin: 0.5em 0;
    }

    ul,
    ol {
      padding-left: 20px;
      margin: 0.5em 0;
    }

    ul {
      list-style-type: disc;
    }

    ol {
      list-style-type: decimal;
    }

    li {
      margin-bottom: 0.3em;
    }
  }

  .buttonContainer {
    display: flex;
    gap: 15px;
  }

  .downloadBtn {
    width: 200px;
    height: 40px;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 15px;

    &:hover {
      opacity: 0.8;
    }
  }

  .skipBtn {
    width: 200px;
    height: 40px;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    color: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    border: 1px solid #ddd;

    &:hover {
      opacity: 0.8;
      background-color: #f5f5f5;
    }
  }
}