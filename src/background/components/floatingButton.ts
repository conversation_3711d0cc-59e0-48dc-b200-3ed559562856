import { EFloatButtonActionType, EContentsMessageType } from '@src/common/const'
import controlSidePanel, {
  type TMessageSender,
} from '../utils/controlSidePanel'

const handleScreenShot = async (
  sender: TMessageSender,
  sendResponse: (response?: any) => void,
  payload?: {
    rect?: { x: number; y: number; width: number; height: number }
    viewport?: { width: number; height: number; dpr: number }
  }
) => {
  try {
    chrome.tabs
      .captureVisibleTab(sender.tab?.windowId ?? chrome.windows.WINDOW_ID_CURRENT, {
        format: 'png',
        quality: 100,
      })
      .then(async (dataUrl) => {
        try {
          if (payload?.rect && payload?.viewport) {
            const { rect, viewport } = payload
            const dpr = viewport.dpr || 1
            // 将页面坐标转换为实际图像像素坐标
            const sx = Math.round(rect.x * dpr)
            const sy = Math.round(rect.y * dpr)
            const sw = Math.round(rect.width * dpr)
            const sh = Math.round(rect.height * dpr)

            const imgBitmap = await createImageBitmap(await (await fetch(dataUrl)).blob())

            const canvas = new OffscreenCanvas(sw, sh)
            const ctx = canvas.getContext('2d')
            if (!ctx) throw new Error('Canvas 2D context not available')
            ctx.drawImage(imgBitmap, sx, sy, sw, sh, 0, 0, sw, sh)
            const croppedBlob = await canvas.convertToBlob({ type: 'image/png', quality: 1 })
            const croppedUrl = await new Promise<string>((resolve) => {
              const fr = new FileReader()
              fr.onload = () => resolve(String(fr.result))
              fr.readAsDataURL(croppedBlob)
            })
            sendResponse({ ok: true, dataUrl: croppedUrl })
            return
          }
          // 无裁剪数据，直接返回原图
          sendResponse({ ok: true, dataUrl })
        } catch (cropErr) {
          console.error('crop screenshot error:', cropErr)
          // 裁剪失败，返回原始截图作为兜底
          sendResponse({ ok: true, dataUrl })
        }
      })
      .catch((err) => {
        console.error('captureVisibleTab error:', err)
        sendResponse({ ok: false, error: String(err) })
      })
  } catch (e) {
    console.error('captureVisibleTab exception:', e)
    sendResponse({ ok: false, error: String(e) })
  }
}

/**
 *
 * @param action 悬浮球的操作类型
 */
export const handleFloatingButtonAction = async (
  message: {
    type: EContentsMessageType.FloatingButton
    data: any
  },
  sender: TMessageSender,
  sendResponse: (response?: any) => void
) => {
  const { data } = message
  const action = data.action

  const { toggleSidePanel, openSidePanel, isSidePanelOpen } = controlSidePanel
  const delayTime = isSidePanelOpen ? 0 : 1000 // 如果侧边栏已经打开，则不需要延迟

  switch (data.action) {
    case EFloatButtonActionType.OpenPanel:
      // 处理侧边栏面板
      await toggleSidePanel(sender)
      break
    case EFloatButtonActionType.Translate:
      // 处理翻译操作
      await openSidePanel(sender)
      // 向侧边栏发送消息，请求翻译
      setTimeout(() => {
        chrome.runtime.sendMessage({
          type: EFloatButtonActionType.Translate,
        })
      }, delayTime)
      console.log('处理翻译操作')
      break
    case EFloatButtonActionType.Screenshot:
      // 处理截图操作
      handleScreenShot(sender, sendResponse, data)
      await openSidePanel(sender)
      console.log('处理截图操作')
      break
    case EFloatButtonActionType.Summary:
      // 处理总结操作
      await openSidePanel(sender)
      // 向侧边栏发送消息，请求总结
      setTimeout(() => {
        chrome.runtime.sendMessage({
          type: EFloatButtonActionType.Summary,
        })
      }, delayTime)
      break
    case EFloatButtonActionType.XzlParamExtract:
      // 处理新涨乐标注参数提取操作
      // 这个操作不需要打开侧边栏，直接在content script中处理
      console.log('处理新涨乐标注参数提取操作')
      // 参数提取的具体逻辑已经在 webAssistantManager 中处理
      // 这里只需要记录日志，实际处理在 content script 中完成
      break
    default:
      console.warn('未知的悬浮球操作:', action)
  }
}
