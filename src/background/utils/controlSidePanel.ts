export type TMessageSender = chrome.runtime.MessageSender

/**
 * 侧边栏的切换逻辑
 */
export default (() => {
  // 使用Map来跟踪每个窗口的侧边栏状态
  const sidePanelStates = new Map<number, boolean>()

  // 监听窗口关闭事件，清理状态
  chrome.windows.onRemoved.addListener((windowId) => {
    if (sidePanelStates.has(windowId)) {
      console.log(`[SidePanel] 清理已关闭窗口 ${windowId} 的状态`)
      sidePanelStates.delete(windowId)
    }
  })

  // 1. 点击插件图标的事件监听器
  chrome.action.onClicked.addListener(async (tab) => {
    if (!tab.id) return

    await toggleSidePanel({
      tab: { id: tab.id, windowId: tab.windowId },
    } as TMessageSender)
  })

  // 获取指定窗口的侧边栏状态
  function getSidePanelState(windowId: number): boolean {
    return sidePanelStates.get(windowId) || false
  }

  // 设置指定窗口的侧边栏状态
  function setSidePanelState(windowId: number, isOpen: boolean) {
    sidePanelStates.set(windowId, isOpen)
  }

  // 2. 切换侧边栏的函数
  async function toggleSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    const currentState = getSidePanelState(windowId)

    try {
      if (currentState) {
        // 如果侧边栏已经打开，关闭它
        console.log(`[SidePanel] 关闭窗口 ${windowId} 的侧边栏`)
        await closeSidePanel(windowId)
      } else {
        // 如果侧边栏关闭，则打开它
        console.log(`[SidePanel] 打开窗口 ${windowId} 的侧边栏`)
        await openSidePanelForWindow(windowId)
      }
    } catch (error) {
      console.error(`侧边栏操作失败 (窗口 ${windowId}):`, error)
      // 发生错误时重置状态
      setSidePanelState(windowId, false)
    }
  }

  // 关闭侧边栏的内部函数
  async function closeSidePanel(windowId: number) {
    try {
      // 方法1: 先禁用侧边栏
      await chrome.sidePanel.setOptions({
        enabled: false,
      })

      // 更新状态
      setSidePanelState(windowId, false)

      // 等待一小段时间确保关闭操作完成
      await new Promise(resolve => setTimeout(resolve, 200))

      // 重新启用侧边栏，为下次打开做准备
      await chrome.sidePanel.setOptions({
        enabled: true,
      })

      console.log(`[SidePanel] 窗口 ${windowId} 的侧边栏已关闭`)
    } catch (error) {
      console.error(`关闭侧边栏失败 (窗口 ${windowId}):`, error)
      throw error
    }
  }

  // 打开侧边栏的内部函数
  async function openSidePanelForWindow(windowId: number) {
    try {
      await chrome.sidePanel.open({ windowId })
      setSidePanelState(windowId, true)
      console.log(`[SidePanel] 窗口 ${windowId} 的侧边栏已打开`)
    } catch (error) {
      console.error(`打开侧边栏失败 (窗口 ${windowId}):`, error)
      throw error
    }
  }

  async function openSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    try {
      console.log(`[SidePanel] 强制打开窗口 ${windowId} 的侧边栏`)
      await openSidePanelForWindow(windowId)
    } catch (error) {
      console.error("打开侧边栏时出错:", error)
    }
  }

  // 获取侧边栏状态的函数（兼容旧接口）
  function isSidePanelOpen(windowId?: number): boolean {
    if (windowId) {
      return getSidePanelState(windowId)
    }
    // 如果没有指定窗口ID，返回是否有任何窗口的侧边栏是打开的
    return Array.from(sidePanelStates.values()).some(state => state)
  }

  return {
    toggleSidePanel,
    openSidePanel,
    isSidePanelOpen,
    getSidePanelState,
    setSidePanelState,
  }
})()