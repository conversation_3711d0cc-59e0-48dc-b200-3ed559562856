export type TMessageSender = chrome.runtime.MessageSender

/**
 * 侧边栏的切换逻辑
 */
export default (() => {
  let isSidePanelOpen = false

  // 1. 点击插件图标的事件监听器
  chrome.action.onClicked.addListener(async (tab) => {
    if (!tab.id) return

    await toggleSidePanel({
      tab: { id: tab.id, windowId: tab.windowId },
    } as TMessageSender)
  })

  // 2. 切换侧边栏的函数
  async function toggleSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    if (isSidePanelOpen) {
      // 如果侧边栏已经打开，则关闭它
      try {
        await chrome.sidePanel.setOptions({
          enabled: false,
        })
        // 异步设置sidePanel为true，确保下次点击可以打开侧边栏
        chrome.sidePanel.setOptions({
          enabled: true,
        })
        isSidePanelOpen = false // 确认关闭后更新状态
      } catch (error) {
        console.error("关闭侧边栏时出错:", error)
      }
    } else {
      // 如果侧边栏关闭，则打开它
      try {
        await chrome.sidePanel.open({ windowId })
        isSidePanelOpen = true // 确认打开后更新状态
      } catch (error) {
        console.error("打开侧边栏时出错:", error)
      }
    }
  }

  async function openSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    try {
      await chrome.sidePanel.open({ windowId })
      isSidePanelOpen = true // 设置侧边栏状态为打开
    } catch (error) {
      console.error("打开侧边栏时出错:", error)
    }
  }

  return {
    toggleSidePanel,
    openSidePanel,
    isSidePanelOpen: () => isSidePanelOpen,
  }
})()